"use client";

import React from "react";
import { DynamicOccupancyForm } from "@flinkk/components/forms";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@flinkk/components/ui/card";

export default function OccupancyFormTestPage() {
  const handleSubmit = (data: any) => {
    console.log("Occupancy form submitted:", data);
    alert(`Form submitted! Check console for details. Occupancy items: ${data.occupancy.length}`);
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold">Dynamic Occupancy Form Test</h1>
        <p className="text-muted-foreground mt-2">
          Test the dynamic occupancy form with different hotel configurations
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Test with Hotel 1 */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Hotel 1 Configuration</h2>
          <DynamicOccupancyForm
            hotelId="01K1JQ40Z8YWZQHFNXJ5CFBGF8"
            onSubmit={handleSubmit}
            initialValues={{
              "occ_01K1JS7T2KNN5MEM2C1HPWWM5G": 2, // Pre-fill adults
            }}
          />
        </div>

        {/* Test with Hotel 2 */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Hotel 2 Configuration</h2>
          <DynamicOccupancyForm
            hotelId="hotel_2"
            onSubmit={handleSubmit}
          />
        </div>
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-medium">Expected Behavior:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Forms should load occupancy configurations from the API</li>
              <li>Required fields (marked with *) should show validation errors if empty</li>
              <li>Min/max values should be enforced based on configuration</li>
              <li>Only non-zero quantities should be included in the submission payload</li>
              <li>BASE_1 type fields should be hidden by default</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium">Test Cases:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>Try submitting with empty required fields</li>
              <li>Enter values outside the min/max range</li>
              <li>Submit with valid data and check console output</li>
              <li>Test with different hotel IDs to see different configurations</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium">Expected Payload Format:</h4>
            <pre className="bg-muted p-3 rounded text-xs overflow-x-auto">
{`{
  "occupancy": [
    {
      "occupancy_config_id": "occ_01K1JS7T2KNN5MEM2C1HPWWM5G",
      "quantity": 2
    },
    {
      "occupancy_config_id": "occ_01K1JS7TAD1GPSR6BP8K16Y79F", 
      "quantity": 1
    }
  ]
}`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
