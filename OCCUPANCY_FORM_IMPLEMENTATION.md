# Dynamic Occupancy Form Implementation

## Overview

This implementation provides a complete solution for rendering dynamic form fields based on occupancy configurations fetched from the Inventory API. The solution includes API endpoints, React components, validation logic, and proper data transformation.

## 🏗️ Architecture

### 1. API Layer
- **Endpoint**: `GET /api/hotel-management/occupancy-config?hotel_id={hotelId}`
- **Inventory API Method**: `getHotelOccupancyConfig(params)`
- **Response Format**: `{ occupancy_configs: OccupancyConfig[] }`

### 2. Data Layer
- **Custom Hook**: `useOccupancyConfig` for fetching and caching occupancy configs
- **Type Definitions**: Updated `OccupancyConfig` interface to match API response

### 3. Component Layer
- **DynamicOccupancyForm**: Standalone form component
- **OccupancyFields**: Reusable field components for embedding in larger forms
- **Helper Functions**: Schema creation and data transformation utilities

## 📁 Files Created/Modified

### New Files
```
libs/flinkk-inventory/index.ts                          # Added getHotelOccupancyConfig method
apps/frontend/app/api/hotel-management/occupancy-config/route.ts  # API endpoint
packages/hooks/query/use-occupancy-config.ts            # React Query hook
packages/components/forms/dynamic-occupancy-form.tsx    # Standalone form
packages/components/forms/occupancy-fields.tsx          # Reusable fields
packages/components/forms/examples/occupancy-form-example.tsx  # Usage example
packages/components/forms/index.ts                      # Exports
packages/components/forms/README.md                     # Documentation
apps/frontend/app/test/occupancy-form/page.tsx         # Test page
```

### Modified Files
```
packages/hooks/index.ts                                 # Added hook export
```

## 🔧 Key Features

### Dynamic Field Rendering
- Automatically generates form fields based on API-provided occupancy configs
- Maps occupancy types to user-friendly labels (Adults, Children, etc.)
- Hides BASE_1 type fields by default

### Validation Logic
- **Min/Max Values**: Enforced via `min_occupancy` and `max_occupancy`
- **Required Fields**: Fields with `is_default: true` are marked as required
- **Non-negative**: All fields must be 0 or greater
- **Custom Messages**: Descriptive error messages for each validation rule

### Data Transformation
- **Form to API**: Converts form values to expected payload format
- **Filtering**: Only includes non-zero quantities in submission
- **Type Safety**: Full TypeScript support throughout

## 🎯 Usage Examples

### Standalone Form
```tsx
import { DynamicOccupancyForm } from "@flinkk/components/forms";

function BookingPage() {
  const handleSubmit = (data) => {
    console.log("Occupancy data:", data.occupancy);
  };

  return (
    <DynamicOccupancyForm
      hotelId="hotel_123"
      onSubmit={handleSubmit}
      initialValues={{ "occ_123": 2 }}
    />
  );
}
```

### Embedded Fields
```tsx
import { OccupancyFields, createOccupancySchema } from "@flinkk/components/forms";

function BookingForm() {
  const form = useForm({
    resolver: zodResolver(z.object({
      guestName: z.string().min(1),
      ...createOccupancySchema(occupancyConfigs)
    }))
  });

  return (
    <form>
      <input {...form.register("guestName")} />
      <OccupancyFields
        hotelId="hotel_123"
        control={form.control}
      />
    </form>
  );
}
```

## 📊 Data Flow

### 1. API Request
```
GET /api/hotel-management/occupancy-config?hotel_id=hotel_123
```

### 2. API Response
```json
{
  "occupancy_configs": [
    {
      "id": "occ_01K1JS7T2KNN5MEM2C1HPWWM5G",
      "name": "Adult",
      "type": "EXTRA_ADULT",
      "min_occupancy": 1,
      "max_occupancy": 1,
      "is_default": true
    }
  ]
}
```

### 3. Form Rendering
- Dynamic fields generated based on configs
- Validation rules applied automatically
- User-friendly labels displayed

### 4. Form Submission
```json
{
  "occupancy": [
    {
      "occupancy_config_id": "occ_01K1JS7T2KNN5MEM2C1HPWWM5G",
      "quantity": 2
    }
  ]
}
```

## 🧪 Testing

### Test Page
Visit `/test/occupancy-form` to test the implementation with different hotel configurations.

### Test Cases
1. **Loading State**: Verify spinner shows while fetching configs
2. **Validation**: Test required fields and min/max constraints
3. **Submission**: Check payload format and data transformation
4. **Error Handling**: Test with invalid hotel IDs

## 🔄 Integration Points

### Existing Systems
- **@flinkk/inventory-api**: Extended with new method
- **@flinkk/dynamic-form**: Uses existing NumberFieldFormElement
- **React Hook Form**: Integrates with existing form patterns
- **Zod**: Uses existing validation patterns

### Future Enhancements
- **Caching**: Occupancy configs are cached for 5 minutes
- **Error Recovery**: Graceful handling of API failures
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Internationalization**: Ready for i18n implementation

## 🚀 Deployment Notes

### Dependencies
- All required dependencies are already present in the codebase
- No new package installations required

### Environment
- Works with existing inventory API configuration
- Respects tenant-based API access patterns

### Performance
- Uses React Query for efficient caching
- Minimal re-renders with proper memoization
- Lazy loading of occupancy configs

## 📝 Next Steps

1. **Integration**: Use the components in actual booking/quotation forms
2. **Testing**: Add unit tests for components and utilities
3. **Documentation**: Update team documentation with usage guidelines
4. **Monitoring**: Add logging for API usage and error tracking
