"use client";

import React from "react";
import { Control, FieldValues, FieldPath } from "react-hook-form";
import * as z from "zod";
import { Loader2Icon } from "lucide-react";
import { NumberFieldFormElement } from "@flinkk/dynamic-form/form-elements";
import { useOccupancyConfig } from "@flinkk/hooks/query/use-occupancy-config";
import { OccupancyConfig } from "@flinkk/inventory-api";

// Type mapping for UI labels
const TYPE_LABELS: Record<string, string> = {
  EXTRA_ADULT: "Adults",
  CHILD: "Children",
  INFANT: "Infant",
  EXTRA_BED: "Extra Bed",
  COT: "Baby Cot",
  EXTRA_ADULT_BEYOND_CAPACITY: "Extra Adult (Overflow)",
  BASE_1: "Base", // Hidden/ignored if needed
};

interface OccupancyFieldsProps<
  TFieldValues extends FieldValues = FieldValues,
> {
  hotelId: string;
  control: Control<TFieldValues>;
  disabled?: boolean;
  hideBaseFields?: boolean;
  fieldNamePrefix?: string;
}

export function OccupancyFields<
  TFieldValues extends FieldValues = FieldValues,
>({
  hotelId,
  control,
  disabled = false,
  hideBaseFields = true,
  fieldNamePrefix = "occupancy_",
}: OccupancyFieldsProps<TFieldValues>) {
  // Fetch occupancy configs
  const {
    data: occupancyData,
    isLoading,
    error,
  } = useOccupancyConfig({
    hotelId,
    enabled: !!hotelId,
  });

  const occupancyConfigs = occupancyData?.occupancy_configs || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2Icon className="h-5 w-5 animate-spin" />
        <span className="ml-2 text-sm text-muted-foreground">
          Loading occupancy configuration...
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 text-sm text-destructive">
        Failed to load occupancy configuration. Please try again.
      </div>
    );
  }

  if (occupancyConfigs.length === 0) {
    return (
      <div className="p-4 text-sm text-muted-foreground">
        No occupancy configuration found for this hotel.
      </div>
    );
  }

  const filteredConfigs = hideBaseFields
    ? occupancyConfigs.filter((config) => config.type !== "BASE_1")
    : occupancyConfigs;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {filteredConfigs.map((config) => (
        <NumberFieldFormElement
          key={config.id}
          control={control}
          name={`${fieldNamePrefix}${config.id}` as FieldPath<TFieldValues>}
          label={`${TYPE_LABELS[config.type] || config.name}${
            config.is_default ? " *" : ""
          }`}
          placeholder={`Enter ${TYPE_LABELS[config.type] || config.name.toLowerCase()}`}
          required={config.is_default}
          min={config.min_occupancy}
          max={config.max_occupancy}
          disabled={disabled}
          helperText={
            config.min_occupancy > 0 || config.max_occupancy
              ? `Range: ${config.min_occupancy} - ${config.max_occupancy}`
              : undefined
          }
        />
      ))}
    </div>
  );
}

// Helper function to create Zod schema for occupancy fields
export function createOccupancySchema(occupancyConfigs: OccupancyConfig[]) {
  const schemaFields: Record<string, any> = {};

  occupancyConfigs.forEach((config) => {
    let fieldSchema = z.number().min(0, `${config.name} cannot be negative`);

    // Add min/max validation based on config
    if (config.min_occupancy > 0) {
      fieldSchema = fieldSchema.min(
        config.min_occupancy,
        `${config.name} must be at least ${config.min_occupancy}`
      );
    }

    if (config.max_occupancy) {
      fieldSchema = fieldSchema.max(
        config.max_occupancy,
        `${config.name} cannot exceed ${config.max_occupancy}`
      );
    }

    // Make required fields non-zero if is_default is true
    if (config.is_default) {
      fieldSchema = fieldSchema.min(
        Math.max(1, config.min_occupancy),
        `${config.name} is required`
      );
    }

    schemaFields[`occupancy_${config.id}`] = fieldSchema;
  });

  return schemaFields;
}

// Helper function to transform form data to API payload
export function transformOccupancyData(
  formData: Record<string, any>,
  occupancyConfigs: OccupancyConfig[],
  fieldNamePrefix: string = "occupancy_"
): Array<{ occupancy_config_id: string; quantity: number }> {
  return occupancyConfigs
    .filter((config) => {
      const fieldName = `${fieldNamePrefix}${config.id}`;
      return formData[fieldName] && formData[fieldName] > 0;
    })
    .map((config) => ({
      occupancy_config_id: config.id,
      quantity: formData[`${fieldNamePrefix}${config.id}`],
    }));
}
