# Dynamic Occupancy Form Components

This package provides React components for rendering dynamic form fields based on occupancy configurations fetched from the Inventory API.

## Features

- 🔄 **Dynamic Field Rendering**: Automatically generates form fields based on API-provided occupancy configs
- ✅ **Built-in Validation**: Respects `min_occupancy`, `max_occupancy`, and `is_default` constraints
- 🎨 **Flexible UI**: Provides both standalone form and reusable field components
- 📝 **Type-Safe**: Full TypeScript support with proper type definitions
- 🔧 **Customizable**: Configurable field prefixes, labels, and validation rules

## Components

### 1. `DynamicOccupancyForm`

A complete, standalone form component for occupancy data entry.

```tsx
import { DynamicOccupancyForm } from "@flinkk/components/forms";

function BookingPage() {
  const handleSubmit = (data) => {
    console.log("Occupancy data:", data);
    // data.occupancy = [
    //   { occupancy_config_id: "occ_123", quantity: 2 },
    //   { occupancy_config_id: "occ_456", quantity: 1 }
    // ]
  };

  return (
    <DynamicOccupancyForm
      hotelId="hotel_123"
      onSubmit={handleSubmit}
      initialValues={{ "occ_123": 2 }}
      isSubmitting={false}
    />
  );
}
```

### 2. `OccupancyFields`

Reusable field components that can be embedded in larger forms.

```tsx
import { useForm } from "react-hook-form";
import { OccupancyFields, createOccupancySchema } from "@flinkk/components/forms";

function BookingForm() {
  const form = useForm({
    resolver: zodResolver(z.object({
      guestName: z.string().min(1),
      ...createOccupancySchema(occupancyConfigs)
    }))
  });

  return (
    <form>
      {/* Other form fields */}
      <input {...form.register("guestName")} />
      
      {/* Dynamic occupancy fields */}
      <OccupancyFields
        hotelId="hotel_123"
        control={form.control}
        fieldNamePrefix="occupancy_"
      />
    </form>
  );
}
```

## API Integration

### Endpoint

The components automatically fetch occupancy configurations from:

```
GET /api/hotel-management/occupancy-config?hotel_id={hotelId}
```

### Response Format

```json
{
  "occupancy_configs": [
    {
      "id": "occ_01K1JS7T2KNN5MEM2C1HPWWM5G",
      "name": "Adult",
      "type": "EXTRA_ADULT",
      "hotel_id": "01K1JQ40Z8YWZQHFNXJ5CFBGF8",
      "min_age": 18,
      "max_age": 120,
      "min_occupancy": 1,
      "max_occupancy": 1,
      "is_default": true,
      "metadata": null,
      "created_at": "2025-08-01T12:20:48.851Z",
      "updated_at": "2025-08-01T12:20:48.851Z",
      "deleted_at": null
    }
  ]
}
```

## Field Type Mapping

The components automatically map occupancy types to user-friendly labels:

| Type | UI Label | Required | Notes |
|------|----------|----------|-------|
| `EXTRA_ADULT` | Adults | ✅ | Default |
| `CHILD` | Children | ❌ | Optional |
| `INFANT` | Infant | ❌ | Optional |
| `EXTRA_BED` | Extra Bed | ❌ | Optional |
| `COT` | Baby Cot | ❌ | Optional |
| `EXTRA_ADULT_BEYOND_CAPACITY` | Extra Adult (Overflow) | ❌ | Optional |
| `BASE_1` | Base | ❌ | Hidden by default |

## Validation Rules

The components automatically apply validation based on occupancy config properties:

- **Minimum Values**: Enforced via `min_occupancy` property
- **Maximum Values**: Enforced via `max_occupancy` property  
- **Required Fields**: Fields with `is_default: true` are marked as required
- **Non-negative**: All fields must be 0 or greater

## Data Transformation

### Form Data to API Payload

The `transformOccupancyData` helper function converts form values to the expected API format:

```tsx
// Form data
const formData = {
  occupancy_occ_123: 2,
  occupancy_occ_456: 1,
  occupancy_occ_789: 0  // Will be filtered out
};

// Transformed payload
const payload = transformOccupancyData(formData, occupancyConfigs);
// Result:
// [
//   { occupancy_config_id: "occ_123", quantity: 2 },
//   { occupancy_config_id: "occ_456", quantity: 1 }
// ]
```

## Customization

### Custom Field Prefixes

```tsx
<OccupancyFields
  hotelId="hotel_123"
  control={form.control}
  fieldNamePrefix="custom_prefix_"  // Default: "occupancy_"
/>
```

### Hide Base Fields

```tsx
<OccupancyFields
  hotelId="hotel_123"
  control={form.control}
  hideBaseFields={true}  // Default: true (hides BASE_1 type)
/>
```

### Custom Validation Schema

```tsx
import { createOccupancySchema } from "@flinkk/components/forms";

const customSchema = z.object({
  // Your other fields
  guestName: z.string().min(1),
  
  // Add occupancy validation
  ...createOccupancySchema(occupancyConfigs)
});
```

## Error Handling

The components handle various error states:

- **Loading State**: Shows spinner while fetching configs
- **API Errors**: Displays error message if fetch fails
- **No Configs**: Shows message when no configs are available
- **Validation Errors**: Individual field validation with helpful messages

## Dependencies

- `@flinkk/hooks/query/use-occupancy-config` - Custom hook for fetching configs
- `@flinkk/dynamic-form/form-elements` - Form field components
- `@flinkk/inventory-api` - Type definitions
- `react-hook-form` - Form state management
- `zod` - Schema validation

## Usage Examples

See the complete example in `examples/occupancy-form-example.tsx` for a full implementation showing how to integrate occupancy fields into a larger booking form.
